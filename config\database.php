<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'skul_education_db');

// First, create database if it doesn't exist
try {
    $pdo_temp = new PDO("mysql:host=" . DB_HOST . ";charset=utf8mb4", DB_USERNAME, DB_PASSWORD);
    $pdo_temp->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create database if not exists
    $pdo_temp->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

    // Now connect to the specific database
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USERNAME, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Function to execute queries
function executeQuery($sql, $params = []) {
    global $pdo;
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch(PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        return false;
    }
}

// Function to fetch single row
function fetchRow($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetch() : false;
}

// Function to fetch all rows
function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetchAll() : false;
}

// Function to get last insert ID
function getLastInsertId() {
    global $pdo;
    return $pdo->lastInsertId();
}

// Function to begin transaction
function beginTransaction() {
    global $pdo;
    return $pdo->beginTransaction();
}

// Function to commit transaction
function commitTransaction() {
    global $pdo;
    return $pdo->commit();
}

// Function to rollback transaction
function rollbackTransaction() {
    global $pdo;
    return $pdo->rollBack();
}

// Create database tables if they don't exist
function createTables() {
    global $pdo;
    
    // Users table
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'teacher', 'student', 'staff') DEFAULT 'student',
        user_type VARCHAR(50) NULL,
        user_id BIGINT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        avatar VARCHAR(255) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_role_active (role, is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    
    // Students table
    $sql = "CREATE TABLE IF NOT EXISTS students (
        id INT AUTO_INCREMENT PRIMARY KEY,
        roll VARCHAR(50) UNIQUE NULL,
        name VARCHAR(255) NOT NULL,
        regi VARCHAR(50) UNIQUE NULL,
        `group` VARCHAR(100) NULL,
        fname VARCHAR(255) NULL COMMENT 'Father name',
        mname VARCHAR(255) NULL COMMENT 'Mother name',
        gender ENUM('Male', 'Female', 'Other') NULL,
        dob DATE NULL COMMENT 'Date of birth',
        session VARCHAR(20) NULL,
        sub1 VARCHAR(100) NULL COMMENT 'Subject 1',
        sub2 VARCHAR(100) NULL COMMENT 'Subject 2',
        sub3 VARCHAR(100) NULL COMMENT 'Subject 3',
        4th_sub VARCHAR(100) NULL COMMENT '4th Subject',
        picture VARCHAR(255) NULL,
        phone VARCHAR(20) NULL,
        email VARCHAR(255) UNIQUE NULL,
        address TEXT NULL,
        blood_group VARCHAR(10) NULL,
        religion VARCHAR(50) NULL,
        nationality VARCHAR(50) DEFAULT 'Bangladeshi',
        admission_date DATE NULL,
        previous_school VARCHAR(255) NULL,
        previous_gpa DECIMAL(3,2) NULL,
        status ENUM('Active', 'Inactive', 'Graduated', 'Transferred') DEFAULT 'Active',
        guardian_phone VARCHAR(20) NULL,
        guardian_email VARCHAR(255) NULL,
        guardian_address TEXT NULL,
        emergency_contact VARCHAR(20) NULL,
        medical_info TEXT NULL,
        notes TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_roll_regi_session (roll, regi, session),
        INDEX idx_group_status (`group`, status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    
    // Teachers table
    $sql = "CREATE TABLE IF NOT EXISTS teachers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        employee_id VARCHAR(50) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NULL,
        phone VARCHAR(20) NULL,
        gender ENUM('Male', 'Female', 'Other') NOT NULL,
        dob DATE NULL,
        address TEXT NULL,
        designation VARCHAR(100) NOT NULL,
        department VARCHAR(100) NOT NULL,
        qualification VARCHAR(255) NULL,
        specialization VARCHAR(255) NULL,
        joining_date DATE NOT NULL,
        salary DECIMAL(10,2) NULL,
        blood_group VARCHAR(10) NULL,
        religion VARCHAR(50) NULL,
        nationality VARCHAR(50) DEFAULT 'Bangladeshi',
        nid VARCHAR(20) UNIQUE NULL,
        picture VARCHAR(255) NULL,
        status ENUM('Active', 'Inactive', 'Resigned', 'Retired') DEFAULT 'Active',
        emergency_contact VARCHAR(20) NULL,
        experience TEXT NULL,
        notes TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_employee_dept_status (employee_id, department, status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    
    // Exams table
    $sql = "CREATE TABLE IF NOT EXISTS exams (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(100) NOT NULL,
        session VARCHAR(20) NOT NULL,
        `group` VARCHAR(100) NULL,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        description TEXT NULL,
        status ENUM('Scheduled', 'Ongoing', 'Completed', 'Cancelled') DEFAULT 'Scheduled',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_session_type_group (session, type, `group`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    
    // Fees table
    $sql = "CREATE TABLE IF NOT EXISTS fees (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(100) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        session VARCHAR(20) NOT NULL,
        `group` VARCHAR(100) NULL,
        description TEXT NULL,
        due_date DATE NULL,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_session_type_status (session, type, status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    
    // Fee payments table
    $sql = "CREATE TABLE IF NOT EXISTS fee_payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        fee_id INT NOT NULL,
        amount_paid DECIMAL(10,2) NOT NULL,
        payment_date DATE NOT NULL,
        payment_method VARCHAR(50) NOT NULL,
        receipt_number VARCHAR(50) UNIQUE NOT NULL,
        transaction_id VARCHAR(100) NULL,
        remarks TEXT NULL,
        collected_by INT NULL,
        status ENUM('Paid', 'Partial', 'Pending', 'Cancelled') DEFAULT 'Paid',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
        FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE,
        FOREIGN KEY (collected_by) REFERENCES teachers(id) ON DELETE SET NULL,
        INDEX idx_student_fee_date (student_id, fee_id, payment_date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    
    // Notices table
    $sql = "CREATE TABLE IF NOT EXISTS notices (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        type VARCHAR(100) NOT NULL,
        priority VARCHAR(50) NOT NULL,
        publish_date DATE NOT NULL,
        expiry_date DATE NULL,
        target_audience VARCHAR(100) NULL,
        attachment VARCHAR(255) NULL,
        created_by INT NOT NULL,
        is_published BOOLEAN DEFAULT FALSE,
        send_notification BOOLEAN DEFAULT FALSE,
        status ENUM('Draft', 'Published', 'Expired', 'Archived') DEFAULT 'Draft',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES teachers(id) ON DELETE CASCADE,
        INDEX idx_type_priority_status (type, priority, status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
}

// Initialize database
createTables();

// Insert default admin user if not exists
$adminExists = fetchRow("SELECT id FROM users WHERE email = '<EMAIL>'");
if (!$adminExists) {
    $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
    executeQuery("INSERT INTO users (name, email, password, role, is_active) VALUES (?, ?, ?, ?, ?)", 
                ['System Administrator', '<EMAIL>', $hashedPassword, 'admin', 1]);
}
?>
