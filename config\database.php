<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'skul_education_db');

// Auto setup database and tables
try {
    // First, connect without database to create it
    $pdo_temp = new PDO("mysql:host=" . DB_HOST . ";charset=utf8mb4", DB_USERNAME, DB_PASSWORD);
    $pdo_temp->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create database if not exists
    $pdo_temp->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

    // Now connect to the specific database
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USERNAME, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // Auto create tables if they don't exist
    autoCreateTables($pdo);

} catch(PDOException $e) {
    echo "
    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; border: 1px solid #dc3545; border-radius: 10px; background: #f8d7da; color: #721c24;'>
        <h3><i>⚠️ Database Connection Error</i></h3>
        <p><strong>সমস্যা:</strong> " . $e->getMessage() . "</p>
        <p><strong>সমাধান:</strong></p>
        <ol>
            <li>XAMPP Control Panel খুলুন</li>
            <li>Apache এবং MySQL চালু করুন</li>
            <li>পেজ রিফ্রেশ করুন</li>
        </ol>
        <p><a href='setup-database.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Manual Setup</a></p>
    </div>";
    exit();
}

// Auto create tables function
function autoCreateTables($pdo) {
    // Users table
    $pdo->exec("CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'teacher', 'student', 'staff') DEFAULT 'student',
        user_type VARCHAR(50) NULL,
        user_id BIGINT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        avatar VARCHAR(255) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_role_active (role, is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Students table
    $pdo->exec("CREATE TABLE IF NOT EXISTS students (
        id INT AUTO_INCREMENT PRIMARY KEY,
        roll VARCHAR(50) UNIQUE NULL,
        name VARCHAR(255) NOT NULL,
        regi VARCHAR(50) UNIQUE NULL,
        `group` VARCHAR(100) NULL,
        fname VARCHAR(255) NULL COMMENT 'Father name',
        mname VARCHAR(255) NULL COMMENT 'Mother name',
        gender ENUM('Male', 'Female', 'Other') NULL,
        dob DATE NULL COMMENT 'Date of birth',
        session VARCHAR(20) NULL,
        sub1 VARCHAR(100) NULL COMMENT 'Subject 1',
        sub2 VARCHAR(100) NULL COMMENT 'Subject 2',
        sub3 VARCHAR(100) NULL COMMENT 'Subject 3',
        4th_sub VARCHAR(100) NULL COMMENT '4th Subject',
        picture VARCHAR(255) NULL,
        phone VARCHAR(20) NULL,
        email VARCHAR(255) UNIQUE NULL,
        address TEXT NULL,
        blood_group VARCHAR(10) NULL,
        religion VARCHAR(50) NULL,
        nationality VARCHAR(50) DEFAULT 'Bangladeshi',
        admission_date DATE NULL,
        previous_school VARCHAR(255) NULL,
        previous_gpa DECIMAL(3,2) NULL,
        status ENUM('Active', 'Inactive', 'Graduated', 'Transferred') DEFAULT 'Active',
        guardian_phone VARCHAR(20) NULL,
        guardian_email VARCHAR(255) NULL,
        guardian_address TEXT NULL,
        emergency_contact VARCHAR(20) NULL,
        medical_info TEXT NULL,
        notes TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_roll_regi_session (roll, regi, session),
        INDEX idx_group_status (`group`, status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Teachers table
    $pdo->exec("CREATE TABLE IF NOT EXISTS teachers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        employee_id VARCHAR(50) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NULL,
        phone VARCHAR(20) NULL,
        gender ENUM('Male', 'Female', 'Other') NOT NULL,
        dob DATE NULL,
        address TEXT NULL,
        designation VARCHAR(100) NOT NULL,
        department VARCHAR(100) NOT NULL,
        qualification VARCHAR(255) NULL,
        specialization VARCHAR(255) NULL,
        joining_date DATE NOT NULL,
        salary DECIMAL(10,2) NULL,
        blood_group VARCHAR(10) NULL,
        religion VARCHAR(50) NULL,
        nationality VARCHAR(50) DEFAULT 'Bangladeshi',
        nid VARCHAR(20) UNIQUE NULL,
        picture VARCHAR(255) NULL,
        status ENUM('Active', 'Inactive', 'Resigned', 'Retired') DEFAULT 'Active',
        emergency_contact VARCHAR(20) NULL,
        experience TEXT NULL,
        notes TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_employee_dept_status (employee_id, department, status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Exams table
    $pdo->exec("CREATE TABLE IF NOT EXISTS exams (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(100) NOT NULL,
        session VARCHAR(20) NOT NULL,
        `group` VARCHAR(100) NULL,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        description TEXT NULL,
        status ENUM('Scheduled', 'Ongoing', 'Completed', 'Cancelled') DEFAULT 'Scheduled',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_session_type_group (session, type, `group`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Fees table
    $pdo->exec("CREATE TABLE IF NOT EXISTS fees (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(100) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        session VARCHAR(20) NOT NULL,
        `group` VARCHAR(100) NULL,
        description TEXT NULL,
        due_date DATE NULL,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_session_type_status (session, type, status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Fee payments table
    $pdo->exec("CREATE TABLE IF NOT EXISTS fee_payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        fee_id INT NOT NULL,
        amount_paid DECIMAL(10,2) NOT NULL,
        payment_date DATE NOT NULL,
        payment_method VARCHAR(50) NOT NULL,
        receipt_number VARCHAR(50) UNIQUE NOT NULL,
        transaction_id VARCHAR(100) NULL,
        remarks TEXT NULL,
        collected_by INT NULL,
        status ENUM('Paid', 'Partial', 'Pending', 'Cancelled') DEFAULT 'Paid',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_student_fee_date (student_id, fee_id, payment_date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Notices table
    $pdo->exec("CREATE TABLE IF NOT EXISTS notices (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        type VARCHAR(100) NOT NULL,
        priority VARCHAR(50) NOT NULL,
        publish_date DATE NOT NULL,
        expiry_date DATE NULL,
        target_audience VARCHAR(100) NULL,
        attachment VARCHAR(255) NULL,
        created_by INT NOT NULL,
        is_published BOOLEAN DEFAULT FALSE,
        send_notification BOOLEAN DEFAULT FALSE,
        status ENUM('Draft', 'Published', 'Expired', 'Archived') DEFAULT 'Draft',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_type_priority_status (type, priority, status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Insert default admin user if not exists
    $adminExists = $pdo->query("SELECT COUNT(*) FROM users WHERE email = '<EMAIL>'")->fetchColumn();
    if ($adminExists == 0) {
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, is_active) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute(['System Administrator', '<EMAIL>', $hashedPassword, 'admin', 1]);
    }

    // Insert sample students if table is empty
    $studentCount = $pdo->query("SELECT COUNT(*) FROM students")->fetchColumn();
    if ($studentCount == 0) {
        $sampleStudents = [
            ['STU24001', 'মোহাম্মদ রহিম', 'REG2024001', 'Science', 'আব্দুল করিম', 'ফাতেমা খাতুন', 'Male', '2005-01-15', '2024'],
            ['STU24002', 'ফাতেমা আক্তার', 'REG2024002', 'Commerce', 'মোহাম্মদ আলী', 'রোকেয়া বেগম', 'Female', '2005-03-20', '2024'],
            ['STU24003', 'আব্দুল্লাহ আল মামুন', 'REG2024003', 'Arts', 'মোহাম্মদ হাসান', 'সালমা খাতুন', 'Male', '2005-05-10', '2024'],
            ['STU24004', 'সালমা খাতুন', 'REG2024004', 'Science', 'মোহাম্মদ রফিক', 'নাসিরা বেগম', 'Female', '2005-07-25', '2024'],
            ['STU24005', 'করিম উদ্দিন', 'REG2024005', 'Commerce', 'আব্দুর রহমান', 'রাবেয়া খাতুন', 'Male', '2005-09-12', '2024']
        ];

        $stmt = $pdo->prepare("INSERT INTO students (roll, name, regi, `group`, fname, mname, gender, dob, session, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'Active')");
        foreach ($sampleStudents as $student) {
            $stmt->execute($student);
        }
    }

    // Insert sample notices if table is empty
    $noticeCount = $pdo->query("SELECT COUNT(*) FROM notices")->fetchColumn();
    if ($noticeCount == 0) {
        $sampleNotices = [
            ['আগামীকাল ছুটির দিন', 'আগামীকাল সরকারি ছুটির কারণে কলেজ বন্ধ থাকবে।', 'General', 'High', date('Y-m-d'), null, 'All', null, 1, 1, 0, 'Published'],
            ['নতুন সেশন ভর্তি শুরু', '২০২৪-২৫ শিক্ষাবর্ষের ভর্তি কার্যক্রম শুরু হয়েছে।', 'Admission', 'Medium', date('Y-m-d'), null, 'All', null, 1, 1, 0, 'Published'],
            ['পরীক্ষার সময়সূচী প্রকাশ', 'আসন্ন বার্ষিক পরীক্ষার সময়সূচী প্রকাশিত হয়েছে।', 'Exam', 'High', date('Y-m-d'), null, 'Students', null, 1, 1, 0, 'Published']
        ];

        $stmt = $pdo->prepare("INSERT INTO notices (title, content, type, priority, publish_date, expiry_date, target_audience, attachment, created_by, is_published, send_notification, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        foreach ($sampleNotices as $notice) {
            $stmt->execute($notice);
        }
    }
}

// Function to execute queries
function executeQuery($sql, $params = []) {
    global $pdo;
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch(PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        return false;
    }
}

// Function to fetch single row
function fetchRow($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetch() : false;
}

// Function to fetch all rows
function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetchAll() : false;
}

// Function to get last insert ID
function getLastInsertId() {
    global $pdo;
    return $pdo->lastInsertId();
}

// Function to begin transaction
function beginTransaction() {
    global $pdo;
    return $pdo->beginTransaction();
}

// Function to commit transaction
function commitTransaction() {
    global $pdo;
    return $pdo->commit();
}

// Function to rollback transaction
function rollbackTransaction() {
    global $pdo;
    return $pdo->rollBack();
}
?>
