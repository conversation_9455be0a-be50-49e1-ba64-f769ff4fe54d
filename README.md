# SKUL - Education Management System

একটি আধুনিক এবং সম্পূর্ণ শিক্ষা প্রতিষ্ঠান ম্যানেজমেন্ট সিস্টেম যা Laravel PHP Framework দিয়ে তৈরি।

## 🌟 বৈশিষ্ট্যসমূহ

### ✅ সম্পন্ন মডিউলসমূহ

#### 🎓 ছাত্র ম্যানেজমেন্ট
- ছাত্র তথ্য যোগ, সম্পাদনা, মুছে ফেলা এবং দেখা
- বাল্ক আপলোড (Excel/CSV)
- ছাত্র তথ্য এক্সপোর্ট
- ছবি আপলোড সুবিধা
- উন্নত অনুসন্ধান এবং ফিল্টার
- ছাত্রের সম্পূর্ণ প্রোফাইল

#### 👨‍🏫 শিক্ষক ম্যানেজমেন্ট (পরিকল্পিত)
- শিক্ষক তথ্য ব্যবস্থাপনা
- পদবী এবং বিভাগ অনুযায়ী সংগঠন

#### 📝 পরীক্ষা ম্যানেজমেন্ট (পরিকল্পিত)
- পরীক্ষার সময়সূচী
- ফলাফল ব্যবস্থাপনা
- গ্রেড গণনা

#### 📅 রুটিন ম্যানেজমেন্ট (পরিকল্পিত)
- ক্লাস রুটিন
- পরীক্ষার রুটিন

#### 💰 ফি ম্যানেজমেন্ট (পরিকল্পিত)
- ফি সংগ্রহ
- পেমেন্ট ট্র্যাকিং
- রিসিট জেনারেশন

#### 👥 কমিটি ম্যানেজমেন্ট (পরিকল্পিত)
- বিভিন্ন কমিটি গঠন
- সদস্য ব্যবস্থাপনা

#### 📢 নোটিশ ম্যানেজমেন্ট (পরিকল্পিত)
- নোটিশ প্রকাশ
- অগ্রাধিকার ভিত্তিক নোটিশ

#### 📊 ড্যাশবোর্ড এবং রিপোর্ট
- ইন্টারঅ্যাক্টিভ ড্যাশবোর্ড
- পরিসংখ্যান এবং চার্ট
- বিভিন্ন রিপোর্ট

#### 🔐 ইউজার অথেন্টিকেশন
- রোল-বেসড অ্যাক্সেস কন্ট্রোল
- অ্যাডমিন, শিক্ষক, ছাত্র ড্যাশবোর্ড
- নিরাপদ লগইন সিস্টেম

## 🛠️ প্রযুক্তি স্ট্যাক

- **Backend**: Laravel 10.x (PHP 8.1+)
- **Frontend**: Bootstrap 5, jQuery, Chart.js
- **Database**: MySQL
- **File Processing**: Maatwebsite Excel
- **PDF Generation**: DomPDF
- **Image Processing**: Intervention Image

## 📋 সিস্টেম প্রয়োজনীয়তা

- PHP >= 8.1
- Composer
- MySQL >= 5.7
- Node.js & NPM (ঐচ্ছিক)
- Apache/Nginx

## 🚀 ইনস্টলেশন গাইড

### 1. প্রজেক্ট ক্লোন করুন
```bash
git clone https://github.com/yourusername/skul.git
cd skul
```

### 2. Dependencies ইনস্টল করুন
```bash
composer install
```

### 3. Environment সেটআপ
```bash
cp .env.example .env
php artisan key:generate
```

### 4. ডাটাবেস কনফিগারেশন
`.env` ফাইলে আপনার ডাটাবেস তথ্য আপডেট করুন:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=skul_education_db
DB_USERNAME=root
DB_PASSWORD=your_password
```

### 5. ডাটাবেস মাইগ্রেশন এবং সিড
```bash
php artisan migrate
php artisan db:seed
```

### 6. Storage Link তৈরি করুন
```bash
php artisan storage:link
```

### 7. সার্ভার চালু করুন
```bash
php artisan serve
```

## 🔑 ডিফল্ট লগইন তথ্য

### অ্যাডমিন
- **Email**: <EMAIL>
- **Password**: admin123

### শিক্ষক
- **Email**: <EMAIL>
- **Password**: teacher123

### ছাত্র
- **Email**: <EMAIL>
- **Password**: student123

## 📁 প্রজেক্ট স্ট্রাকচার

```
skul/
├── app/
│   ├── Http/Controllers/
│   │   ├── Admin/
│   │   │   ├── StudentController.php
│   │   │   ├── DashboardController.php
│   │   │   └── ...
│   │   └── AuthController.php
│   ├── Models/
│   │   ├── Student.php
│   │   ├── Teacher.php
│   │   ├── User.php
│   │   └── ...
│   ├── Http/Requests/
│   │   └── StudentRequest.php
│   ├── Imports/
│   │   └── StudentsImport.php
│   └── Exports/
│       └── StudentsExport.php
├── database/
│   ├── migrations/
│   └── seeders/
├── resources/
│   └── views/
│       ├── layouts/
│       │   └── admin.blade.php
│       ├── admin/
│       │   ├── dashboard.blade.php
│       │   └── students/
│       └── auth/
│           └── login.blade.php
├── routes/
│   └── web.php
├── config/
│   └── education.php
└── public/
    └── uploads/
```

## 🎯 ছাত্র ম্যানেজমেন্ট ফিচার

### ছাত্র তথ্যের ক্ষেত্রসমূহ
- Roll (রোল নম্বর)
- Name (নাম)
- Registration (রেজিস্ট্রেশন নম্বর)
- Group (গ্রুপ)
- Father's Name (পিতার নাম)
- Mother's Name (মাতার নাম)
- Gender (লিঙ্গ)
- Date of Birth (জন্ম তারিখ)
- Session (সেশন)
- Subjects (বিষয়সমূহ)
- Picture (ছবি)
- Contact Information (যোগাযোগের তথ্য)
- Guardian Information (অভিভাবকের তথ্য)

### বাল্ক আপলোড
- Excel/CSV ফাইল সাপোর্ট
- টেমপ্লেট ডাউনলোড
- ভ্যালিডেশন এবং এরর হ্যান্ডলিং
- অনুপস্থিত তথ্যের জন্য ফাঁকা ক্ষেত্র সাপোর্ট

### অনুসন্ধান এবং ফিল্টার
- নাম, রোল, রেজিস্ট্রেশন দিয়ে অনুসন্ধান
- সেশন অনুযায়ী ফিল্টার
- গ্রুপ অনুযায়ী ফিল্টার
- স্ট্যাটাস অনুযায়ী ফিল্টার

## 🔧 কনফিগারেশন

`config/education.php` ফাইলে সিস্টেমের বিভিন্ন সেটিংস কাস্টমাইজ করা যায়:

- প্রতিষ্ঠানের তথ্য
- একাডেমিক সেটিংস
- আপলোড সেটিংস
- ছাত্র/শিক্ষক কনফিগারেশন
- পরীক্ষা এবং গ্রেডিং সিস্টেম

## 🚧 আসন্ন ফিচারসমূহ

- [ ] শিক্ষক ম্যানেজমেন্ট সিস্টেম
- [ ] পরীক্ষা ম্যানেজমেন্ট সিস্টেম
- [ ] রুটিন ম্যানেজমেন্ট সিস্টেম
- [ ] ফি ম্যানেজমেন্ট সিস্টেম
- [ ] কমিটি ম্যানেজমেন্ট সিস্টেম
- [ ] নোটিশ ম্যানেজমেন্ট সিস্টেম
- [ ] উপস্থিতি ব্যবস্থাপনা
- [ ] লাইব্রেরি ম্যানেজমেন্ট
- [ ] ট্রান্সপোর্ট ম্যানেজমেন্ট
- [ ] SMS/Email নোটিফিকেশন
- [ ] মোবাইল অ্যাপ API

## 🤝 অবদান

এই প্রজেক্টে অবদান রাখতে চাইলে:

1. Fork করুন
2. Feature branch তৈরি করুন (`git checkout -b feature/AmazingFeature`)
3. Commit করুন (`git commit -m 'Add some AmazingFeature'`)
4. Push করুন (`git push origin feature/AmazingFeature`)
5. Pull Request তৈরি করুন

## 📝 লাইসেন্স

এই প্রজেক্টটি MIT লাইসেন্সের অধীনে লাইসেন্সপ্রাপ্ত।

## 📞 সাপোর্ট

কোনো সমস্যা বা প্রশ্ন থাকলে:
- Issue তৈরি করুন
- Email: <EMAIL>

## 🙏 কৃতজ্ঞতা

- Laravel Framework
- Bootstrap
- Chart.js
- Font Awesome
- এবং অন্যান্য ওপেন সোর্স লাইব্রেরি

---

**SKUL Education Management System** - আধুনিক শিক্ষা প্রতিষ্ঠানের জন্য সম্পূর্ণ সমাধান।
