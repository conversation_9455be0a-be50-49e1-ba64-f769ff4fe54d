<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

// Get filter parameters
$search = $_GET['search'] ?? '';
$session = $_GET['session'] ?? '';
$group = $_GET['group'] ?? '';
$status = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// Build filters
$filters = [];
if ($search) $filters['search'] = $search;
if ($session) $filters['session'] = $session;
if ($group) $filters['group'] = $group;
if ($status) $filters['status'] = $status;

// Get students with pagination
$students = getStudents($filters);
$totalStudents = count($students);

// Paginate results
$paginatedStudents = array_slice($students, $offset, $limit);

// Get filter options
$sessions = fetchAll("SELECT DISTINCT session FROM students WHERE session IS NOT NULL ORDER BY session DESC");
$groups = ['Science', 'Commerce', 'Arts', 'Vocational'];
$statuses = ['Active', 'Inactive', 'Graduated', 'Transferred'];
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ছাত্র-ছাত্রী ব্যবস্থাপনা - SKUL</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>
    
    <!-- Sidebar -->
    <?php include '../includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="page-title">
                                <i class="fas fa-user-graduate me-2"></i>
                                ছাত্র-ছাত্রী ব্যবস্থাপনা
                            </h2>
                            <p class="text-muted">ছাত্র-ছাত্রীদের তথ্য পরিচালনা এবং ব্যবস্থাপনা করুন</p>
                        </div>
                        <div class="btn-group">
                            <a href="add.php" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> নতুন ছাত্র
                            </a>
                            <a href="bulk-upload.php" class="btn btn-success">
                                <i class="fas fa-upload me-1"></i> বাল্ক আপলোড
                            </a>
                            <a href="export.php" class="btn btn-info">
                                <i class="fas fa-download me-1"></i> এক্সপোর্ট
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">অনুসন্ধান</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="নাম, রোল, রেজিস্ট্রেশন...">
                        </div>
                        <div class="col-md-2">
                            <label for="session" class="form-label">সেশন</label>
                            <select class="form-select" id="session" name="session">
                                <option value="">সব সেশন</option>
                                <?php foreach ($sessions as $s): ?>
                                    <option value="<?php echo $s['session']; ?>" 
                                            <?php echo $session === $s['session'] ? 'selected' : ''; ?>>
                                        <?php echo $s['session']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="group" class="form-label">গ্রুপ</label>
                            <select class="form-select" id="group" name="group">
                                <option value="">সব গ্রুপ</option>
                                <?php foreach ($groups as $g): ?>
                                    <option value="<?php echo $g; ?>" 
                                            <?php echo $group === $g ? 'selected' : ''; ?>>
                                        <?php echo $g; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">অবস্থা</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">সব অবস্থা</option>
                                <?php foreach ($statuses as $st): ?>
                                    <option value="<?php echo $st; ?>" 
                                            <?php echo $status === $st ? 'selected' : ''; ?>>
                                        <?php echo $st; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i> ফিল্টার
                                </button>
                                <a href="index.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i> ক্লিয়ার
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Students Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        ছাত্র-ছাত্রীর তালিকা (<?php echo $totalStudents; ?> জন)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($paginatedStudents)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ছবি</th>
                                        <th>রোল</th>
                                        <th>নাম</th>
                                        <th>রেজিস্ট্রেশন</th>
                                        <th>গ্রুপ</th>
                                        <th>সেশন</th>
                                        <th>যোগাযোগ</th>
                                        <th>অবস্থা</th>
                                        <th>কার্যক্রম</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($paginatedStudents as $student): ?>
                                        <tr>
                                            <td>
                                                <img src="<?php echo $student['picture'] ? '../uploads/students/' . $student['picture'] : '../assets/images/default-avatar.svg'; ?>"
                                                     alt="<?php echo htmlspecialchars($student['name']); ?>" 
                                                     class="rounded-circle" width="40" height="40">
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($student['roll']); ?></strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($student['name']); ?></strong>
                                                    <?php if ($student['gender']): ?>
                                                        <small class="text-muted d-block"><?php echo $student['gender']; ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($student['regi']); ?></td>
                                            <td>
                                                <?php if ($student['group']): ?>
                                                    <span class="badge bg-info"><?php echo $student['group']; ?></span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $student['session']; ?></td>
                                            <td>
                                                <?php if ($student['phone']): ?>
                                                    <small class="d-block"><?php echo $student['phone']; ?></small>
                                                <?php endif; ?>
                                                <?php if ($student['email']): ?>
                                                    <small class="text-muted d-block"><?php echo $student['email']; ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $statusColors = [
                                                    'Active' => 'success',
                                                    'Inactive' => 'secondary',
                                                    'Graduated' => 'primary',
                                                    'Transferred' => 'warning'
                                                ];
                                                $statusColor = $statusColors[$student['status']] ?? 'secondary';
                                                ?>
                                                <span class="badge bg-<?php echo $statusColor; ?>">
                                                    <?php echo $student['status']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="view.php?id=<?php echo $student['id']; ?>" 
                                                       class="btn btn-outline-info" title="দেখুন">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="edit.php?id=<?php echo $student['id']; ?>" 
                                                       class="btn btn-outline-primary" title="সম্পাদনা">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="delete.php?id=<?php echo $student['id']; ?>" 
                                                       class="btn btn-outline-danger delete-btn" 
                                                       title="মুছে ফেলুন"
                                                       data-message="আপনি কি নিশ্চিত এই ছাত্রের তথ্য মুছে ফেলতে চান?">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($totalStudents > $limit): ?>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div>
                                    <small class="text-muted">
                                        <?php echo ($offset + 1); ?> থেকে <?php echo min($offset + $limit, $totalStudents); ?> 
                                        এর মধ্যে <?php echo $totalStudents; ?> জন
                                    </small>
                                </div>
                                <div>
                                    <?php
                                    $totalPages = ceil($totalStudents / $limit);
                                    $queryParams = $_GET;
                                    ?>
                                    <nav>
                                        <ul class="pagination pagination-sm">
                                            <?php if ($page > 1): ?>
                                                <?php $queryParams['page'] = $page - 1; ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?<?php echo http_build_query($queryParams); ?>">পূর্ববর্তী</a>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                                <?php $queryParams['page'] = $i; ?>
                                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                    <a class="page-link" href="?<?php echo http_build_query($queryParams); ?>"><?php echo $i; ?></a>
                                                </li>
                                            <?php endfor; ?>
                                            
                                            <?php if ($page < $totalPages): ?>
                                                <?php $queryParams['page'] = $page + 1; ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?<?php echo http_build_query($queryParams); ?>">পরবর্তী</a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">কোনো ছাত্র-ছাত্রী পাওয়া যায়নি</h5>
                            <p class="text-muted">
                                <?php if (!empty($filters)): ?>
                                    ফিল্টার পরিবর্তন করে আবার চেষ্টা করুন অথবা 
                                    <a href="index.php">সব ফিল্টার ক্লিয়ার করুন</a>
                                <?php else: ?>
                                    <a href="add.php">নতুন ছাত্র যোগ করুন</a>
                                <?php endif; ?>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    
    <script>
        $(document).ready(function() {
            // Auto-submit form on filter change
            $('#session, #group, #status').on('change', function() {
                $(this).closest('form').submit();
            });
            
            // Delete confirmation
            $('.delete-btn').on('click', function(e) {
                e.preventDefault();
                const message = $(this).data('message') || 'আপনি কি নিশ্চিত?';
                if (confirm(message)) {
                    window.location.href = $(this).attr('href');
                }
            });
        });
    </script>
</body>
</html>
